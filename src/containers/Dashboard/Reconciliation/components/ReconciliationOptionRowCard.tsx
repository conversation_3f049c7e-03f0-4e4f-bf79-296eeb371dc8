import React, { useEffect, useState } from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';
import ToolTip from '+containers/Dashboard/Shared/Tooltip';
import LoadingPlaceholder from '+shared/LoadingPlaceHolder';
import { ReconcileColumnSectionType } from '+types';

import ReconciliationOptionRow from '../components/ReconciliationOptionRow';
import { buildReconciliationReportOptions } from '../helpers/reconcileReportHelper';

const ReconciliationOptionRowCard = ({
  isLoading,
  mappings,
  optionKeyMappings,
  removingItems,
  label,
  handleOptionChange,
  handleDelete,
  handleAddNewColumn,
  message,
  fieldType,
  controlFieldDisplay,
  mode,
  handleBulkDelete,
  isItemDisabled,
  noBackground = false
}: {
  isLoading: ReconcileColumnSectionType['isLoading'];
  mappings: ReconcileColumnSectionType['comparisonKeyMappings'];
  optionKeyMappings?: ReconcileColumnSectionType['primaryKeyMappings'];
  removingItems?: ReconcileColumnSectionType['removingItems'];
  handleOptionChange: ReconcileColumnSectionType['handleOptionChange'];
  handleDelete?: ReconcileColumnSectionType['handleDelete'];
  handleAddNewColumn?: ReconcileColumnSectionType['handleAddNewColumn'];
  label: string;
  message?: string;
  fieldType?: 'text' | 'select';
  controlFieldDisplay?: boolean;
  mode?: 'add' | 'remove';
  handleBulkDelete?: (id: string[]) => void;
  isItemDisabled?: (itemId: string) => boolean;
  noBackground?: boolean;
}) => {
  const [displayField, setDisplayField] = useState(false);
  const [selected, setSelected] = useState<string[]>(removingItems ? Array.from(removingItems) : []);
  const handleSelected = (id: string) => {
    if (selected.includes(id)) {
      setSelected(selected.filter(item => item !== id));
    } else {
      setSelected([...selected, id]);
    }
  };

  useEffect(() => {
    handleBulkDelete?.(selected);
  }, [selected.length]);

  return (
    <section>
      <div className="manage-columns--card-label">
        {controlFieldDisplay && <input type="checkbox" checked={displayField} onChange={() => setDisplayField(!displayField)} />}
        <p>{label} </p>
        {message && (
          <ToolTip message={message} className="label-tooltip">
            <Icon name="info" height={12} width={12} />
          </ToolTip>
        )}
      </div>
      <div>
        {isLoading ? (
          <div>
            <LoadingPlaceholder type="text" content={3} />
            <LoadingPlaceholder type="text" content={3} />
          </div>
        ) : (
          ((controlFieldDisplay && displayField) || !controlFieldDisplay) && (
            <div style={!noBackground ? { background: '#F9FBFD', paddingInline: '20px', borderRadius: '8px', paddingBottom: '20px' } : {}}>
              {!noBackground && (
                <div className={'recon-report__content--field --no-border ' + (mode === 'remove' ? '--remove' : '')}>
                  <div className="recon-report__content--field__left">
                    <p className="recon-report__content--field__left--title"> Processor Column</p>
                  </div>
                  <div className="recon-report__content--field__right">
                    <p className="recon-report__content--field__right--title">Matching Kora Column</p>
                  </div>
                </div>
              )}
              <section className="recon-report__content--option">
                {mappings.length > 0 &&
                  mappings.map((item, index) => {
                    const comparisonOptionWithoutCurrent = mappings.filter(option => option.id !== item.id);
                    const isRemoving = removingItems?.has(item.id as string);

                    return (
                      <div
                        key={item.id}
                        className={`reconciliation-option-row-wrapper ${isRemoving && mode !== 'remove' ? 'removing' : ''}`}
                      >
                        <ReconciliationOptionRow
                          options={
                            optionKeyMappings ? buildReconciliationReportOptions(optionKeyMappings, comparisonOptionWithoutCurrent) : []
                          }
                          value={item}
                          onChange={(value, field) => handleOptionChange(value, field, item.id as string)}
                          onDelete={handleDelete && index !== 0 ? () => handleDelete?.(item.id as string) : undefined}
                          fieldType={fieldType}
                          selected={selected.includes(item.id as string) || isRemoving}
                          onSelect={() => handleSelected(item.id as string)}
                          mode={mode}
                          disabled={isItemDisabled?.(item.id as string)}
                        />
                      </div>
                    );
                  })}
              </section>

              {handleAddNewColumn && (
                <button type="button" className="btn btn-light-blue border-dotted" onClick={handleAddNewColumn}>
                  <i className="os-icon os-icon-plus mr-2 font-weight-bolder" />
                  Add new column
                </button>
              )}
            </div>
          )
        )}
      </div>
    </section>
  );
};

export default ReconciliationOptionRowCard;
