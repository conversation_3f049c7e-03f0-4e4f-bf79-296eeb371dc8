import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { beforeEach, describe, expect, it } from 'vitest';

import { mockPrimaryKeyMappings, mockProcessorConfigResponseForReconcile, mockStartReconciliationData } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';
import useReconciliationStore from '+store/reconciliationStore';

import ReconcileReport from '../index';

const MockedReconcileReport = () => {
  return (
    <MockIndex>
      <ReconcileReport />
    </MockIndex>
  );
};

const mockCreateReconciliationResponse = {
  status: true,
  message: 'Reconciliation created successfully',
  data: {
    id: 456,
    kora_id: 'KPY-REC-test123',
    title: 'Test Reconciliation',
    processor: 'korapay',
    payment_type: 'payin',
    status: 'processing',
    createdAt: '2024-01-01T00:00:00.000Z'
  }
};

describe('ReconcileReport', () => {
  beforeEach(() => {
    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', ({ request }) => {
        const url = new URL(request.url);
        const processor = url.searchParams.get('processor');
        const paymentType = url.searchParams.get('payment_type');

        if (processor === 'korapay' && paymentType === 'payin') {
          return HttpResponse.json(mockProcessorConfigResponseForReconcile, { status: 200 });
        }
        return HttpResponse.json({ status: false, data: [] }, { status: 404 });
      }),

      http.post('/admin/settlement-reconciliations', () => {
        return HttpResponse.json(mockCreateReconciliationResponse, { status: 200 });
      })
    );
  });

  it('should be accessible', async () => {
    const { container } = render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    const results = await axe(container, {
      rules: {
        // Address labeling and color contrast issues in the component
      }
    });
    expect(results).toHaveNoViolations();
  });

  it('should render the component with correct heading and description', async () => {
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    expect(screen.getByText(/To start the reconciliation process, map each column/)).toBeInTheDocument();
  });

  it('should render report profile cards', async () => {
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    expect(screen.getAllByText('Uploaded Report')).toHaveLength(2);

    expect(screen.getAllByText('0 columns detected')).toHaveLength(2);
  });

  it('should display loading state while fetching processor config', async () => {
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });
  });

  it('should render comparison key mappings when available', async () => {
    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: [
        {
          id: 'comparison-mapping-1',
          internal_report: 'status',
          processor_report: 'txn_status'
        }
      ]
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    expect(screen.getByText('Column to be reconciled on Kora')).toBeInTheDocument();
  });

  it('should render empty state when no comparison key mappings exist', async () => {
    // Override handler to return config with empty comparison mappings
    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', ({ request }) => {
        const url = new URL(request.url);
        const processor = url.searchParams.get('processor');
        const paymentType = url.searchParams.get('payment_type');
        if (processor === 'korapay' && paymentType === 'payin') {
          return HttpResponse.json(
            {
              status: true,
              data: [
                {
                  ...mockProcessorConfigResponseForReconcile.data[0],
                  comparison_key_mappings: []
                }
              ]
            },
            { status: 200 }
          );
        }
        return HttpResponse.json({ status: true, data: [] }, { status: 200 });
      })
    );

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: []
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('No reconciliation columns')).toBeInTheDocument();
    });

    expect(screen.getByText('Add a column to start reconciliation')).toBeInTheDocument();
  });

  it('should handle adding new column', async () => {
    const user = userEvent.setup();

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: []
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Add new column')).toBeInTheDocument();
    });

    const addButton = screen.getByRole('button', { name: 'Add new column' });
    await user.click(addButton);

    const state = useReconciliationStore.getState();
    expect(state.comparisonKeyMappings.length).toBeGreaterThanOrEqual(1);
    const last = state.comparisonKeyMappings[state.comparisonKeyMappings.length - 1];
    expect(last).toEqual(
      expect.objectContaining({
        processor_report: '',
        internal_report: '',
        id: expect.any(String)
      })
    );
  });

  it('should automatically match corresponding field when auto-match is enabled and one field is selected', async () => {
    // Return mapping with only processor_report set to trigger auto-match of internal_report
    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', ({ request }) => {
        const url = new URL(request.url);
        const processor = url.searchParams.get('processor');
        const paymentType = url.searchParams.get('payment_type');
        if (processor === 'korapay' && paymentType === 'payin') {
          return HttpResponse.json(
            {
              status: true,
              data: [
                {
                  ...mockProcessorConfigResponseForReconcile.data[0],
                  comparison_key_mappings: [{ id: 'test-mapping-1', processor_report: 'txn_id', internal_report: '' }]
                }
              ]
            },
            { status: 200 }
          );
        }
        return HttpResponse.json({ status: true, data: [] }, { status: 200 });
      })
    );

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      autoMatchColumns: true
    } as any);

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    await waitFor(() => {
      const state = useReconciliationStore.getState();
      // Auto-match uses primaryKeyMappings mapping by processor_report
      expect(state.comparisonKeyMappings[0].internal_report).toBe('transaction_id');
    });
  });

  it('should automatically match processor_report when internal_report is selected and auto-match is enabled', async () => {
    // Return mapping with only internal_report set to trigger auto-match of processor_report
    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', ({ request }) => {
        const url = new URL(request.url);
        const processor = url.searchParams.get('processor');
        const paymentType = url.searchParams.get('payment_type');
        if (processor === 'korapay' && paymentType === 'payin') {
          return HttpResponse.json(
            {
              status: true,
              data: [
                {
                  ...mockProcessorConfigResponseForReconcile.data[0],
                  comparison_key_mappings: [{ id: 'test-mapping-2', processor_report: '', internal_report: 'amount' }]
                }
              ]
            },
            { status: 200 }
          );
        }
        return HttpResponse.json({ status: true, data: [] }, { status: 200 });
      })
    );

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      autoMatchColumns: true
    } as any);

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    await waitFor(() => {
      const state = useReconciliationStore.getState();
      // Auto-match uses primaryKeyMappings mapping by internal_report
      expect(state.comparisonKeyMappings[0].processor_report).toBe('amt');
    });
  });

  it('should not auto-match when auto-match is disabled', async () => {
    // Force a mapping that would be auto-matched if enabled, but keep auto-match disabled
    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', ({ request }) => {
        const url = new URL(request.url);
        const processor = url.searchParams.get('processor');
        const paymentType = url.searchParams.get('payment_type');
        if (processor === 'korapay' && paymentType === 'payin') {
          return HttpResponse.json(
            {
              status: true,
              data: [
                {
                  ...mockProcessorConfigResponseForReconcile.data[0],
                  comparison_key_mappings: [{ id: 'test-mapping-3', processor_report: 'txn_id', internal_report: '' }]
                }
              ]
            },
            { status: 200 }
          );
        }
        return HttpResponse.json({ status: true, data: [] }, { status: 200 });
      })
    );

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      autoMatchColumns: false
    } as any);

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const state = useReconciliationStore.getState();
    expect(state.comparisonKeyMappings[0].internal_report).toBe('');
  });

  it('should handle deleting comparison key mapping', async () => {
    const user = userEvent.setup();

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: [
        {
          id: 'comparison-mapping-1',
          internal_report: 'status',
          processor_report: 'txn_status'
        }
      ]
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const deleteButton = screen.getByLabelText('Delete mapping');
    await user.click(deleteButton);

    await waitFor(
      () => {
        const state = useReconciliationStore.getState();
        expect(state.comparisonKeyMappings).toHaveLength(0);
      },
      { timeout: 500 }
    );
  });

  it('should handle preview display toggle', async () => {
    const user = userEvent.setup();
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    const previewButton = screen.getByRole('button', { name: 'Preview' });
    await user.click(previewButton);

    await waitFor(() => {
      expect(screen.getByText('Processor Report Columns')).toBeInTheDocument();
    });
  });

  it('should handle modal close', async () => {
    const user = userEvent.setup();
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    const previewButton = screen.getByRole('button', { name: 'Preview' });
    await user.click(previewButton);

    await waitFor(() => {
      expect(screen.getByText('Processor Report Columns')).toBeInTheDocument();
    });

    const closeButton = screen.getByTestId('close-button');
    await user.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByText('Processor Report Columns')).not.toBeInTheDocument();
    });
  });

  it('should disable start reconciliation button when conditions are not met', async () => {
    // Return config with no comparison mappings to keep button disabled
    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', ({ request }) => {
        const url = new URL(request.url);
        const processor = url.searchParams.get('processor');
        const paymentType = url.searchParams.get('payment_type');
        if (processor === 'korapay' && paymentType === 'payin') {
          return HttpResponse.json(
            {
              status: true,
              data: [
                {
                  ...mockProcessorConfigResponseForReconcile.data[0],
                  comparison_key_mappings: []
                }
              ]
            },
            { status: 200 }
          );
        }
        return HttpResponse.json({ status: true, data: [] }, { status: 200 });
      })
    );

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: []
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Add new column')).toBeInTheDocument();
    });

    const startButton = screen.getByRole('button', { name: 'Start Reconciliation' });
    expect(startButton).toBeDisabled();
  });

  it('should enable start reconciliation button when all conditions are met', async () => {
    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: [
        {
          id: 'comparison-mapping-1',
          internal_report: 'status',
          processor_report: 'txn_status'
        }
      ]
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const startButton = screen.getByRole('button', { name: 'Start Reconciliation' });
    expect(startButton).not.toBeDisabled();
  });

  it('should handle start reconciliation submission and display success modal', async () => {
    const user = userEvent.setup();

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: [
        {
          id: 'comparison-mapping-1',
          internal_report: 'status',
          processor_report: 'txn_status'
        }
      ]
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const startButton = screen.getByRole('button', { name: 'Start Reconciliation' });
    await user.click(startButton);

    await waitFor(() => {
      expect(screen.getByText('Success!')).toBeInTheDocument();
    });

    expect(
      screen.getByText('Your reconciliation request was successful. The link to download the report will be sent to your work email.')
    ).toBeInTheDocument();

    expect(screen.getByTestId('completed-action-button')).toBeInTheDocument();
    expect(screen.getByTestId('completed-action-button')).toHaveTextContent('Dismiss');
  });

  it('should dismiss success modal when Dismiss button is clicked', async () => {
    const user = userEvent.setup();

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: [
        {
          id: 'comparison-mapping-1',
          internal_report: 'status',
          processor_report: 'txn_status'
        }
      ]
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const startButton = screen.getByRole('button', { name: 'Start Reconciliation' });
    await user.click(startButton);

    await waitFor(() => {
      expect(screen.getByText('Success!')).toBeInTheDocument();
    });

    expect(screen.getByTestId('completed-action-button')).toBeInTheDocument();
    expect(screen.getByTestId('completed-action-button')).toHaveTextContent('Dismiss');

    const dismissButton = screen.getByTestId('completed-action-button');
    await user.click(dismissButton);

    await waitFor(() => {
      expect(screen.queryByText('Success!')).not.toBeInTheDocument();
    });
  });

  it('should handle cancel button click', async () => {
    const user = userEvent.setup();

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    const cancelButton = screen.getByRole('button', { name: 'Cancel' });
    await user.click(cancelButton);

    const state = useReconciliationStore.getState();
    expect(state.startReconciliationData).toEqual({});
  });

  it('should handle empty reconciliation data', async () => {
    useReconciliationStore.setState({
      startReconciliationData: {} as any,
      primaryKeyMappings: [],
      comparisonKeyMappings: []
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });
  });

  it('should handle incomplete comparison key mappings', async () => {
    // Force a single incomplete mapping via config
    server.use(
      http.get('/admin/settlement-reconciliations/processor-configs', ({ request }) => {
        const url = new URL(request.url);
        const processor = url.searchParams.get('processor');
        const paymentType = url.searchParams.get('payment_type');
        if (processor === 'korapay' && paymentType === 'payin') {
          return HttpResponse.json(
            {
              status: true,
              data: [
                {
                  ...mockProcessorConfigResponseForReconcile.data[0],
                  comparison_key_mappings: [{ id: 'incomplete-mapping-1', internal_report: '', processor_report: '' }]
                }
              ]
            },
            { status: 200 }
          );
        }
        return HttpResponse.json({ status: true, data: [] }, { status: 200 });
      })
    );

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData
    } as any);

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const startButton = screen.getByRole('button', { name: 'Start Reconciliation' });
    expect(startButton).toBeDisabled();
  });

  it('should display processor report columns in modal', async () => {
    const user = userEvent.setup();
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    const previewButton = screen.getByRole('button', { name: 'Preview' });
    await user.click(previewButton);

    await waitFor(() => {
      expect(screen.getByText('Processor Report Columns')).toBeInTheDocument();
    });

    expect(screen.getByText('txn_id')).toBeInTheDocument();
    expect(screen.getByText('amt')).toBeInTheDocument();
  });

  it('should handle API error during reconciliation creation', async () => {
    const user = userEvent.setup();

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: [
        {
          id: 'api-error-mapping-1',
          internal_report: 'status',
          processor_report: 'txn_status'
        }
      ]
    });

    server.use(
      http.post('/admin/settlement-reconciliations', () => {
        return HttpResponse.json({ status: false, message: 'Failed to create reconciliation' }, { status: 400 });
      })
    );

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const startButton = screen.getByRole('button', { name: 'Start Reconciliation' });
    await user.click(startButton);

    await waitFor(() => {
      expect(startButton).toBeInTheDocument();
    });
  });

  it('should set internal and processor report options from primary key mappings', async () => {
    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
    });

    expect(screen.getAllByText('2 columns detected')).toHaveLength(2);
  });

  it('should handle removing item animation', async () => {
    const user = userEvent.setup();

    useReconciliationStore.setState({
      startReconciliationData: mockStartReconciliationData,
      primaryKeyMappings: mockPrimaryKeyMappings,
      comparisonKeyMappings: [
        {
          id: 'comparison-mapping-1',
          internal_report: 'status',
          processor_report: 'txn_status'
        }
      ]
    });

    render(<MockedReconcileReport />);

    await waitFor(() => {
      expect(screen.getByText('Columns detected on uploaded report')).toBeInTheDocument();
    });

    const deleteButton = screen.getByLabelText('Delete mapping');
    await user.click(deleteButton);

    await waitFor(
      () => {
        const state = useReconciliationStore.getState();
        expect(state.comparisonKeyMappings).toHaveLength(0);
      },
      { timeout: 500 }
    );
  });

  describe('Go Back Functionality', () => {
    it('should show confirmation modal when clicking Go Back button', async () => {
      const user = userEvent.setup();
      render(<MockedReconcileReport />);

      await waitFor(() => {
        expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
      });

      const goBackButton = screen.getByText('Go Back');
      await user.click(goBackButton);

      await waitFor(() => {
        const modalTitle = screen.queryByText('Go back?');
        expect(modalTitle).toBeInTheDocument();
      });

      const confirmationMsg = screen.queryByText(/Please confirm that you want to cancel this reconciliation/i);
      expect(confirmationMsg).toBeInTheDocument();
    });

    it('should close the modal when confirming go back', async () => {
      const user = userEvent.setup();
      render(<MockedReconcileReport />);

      await waitFor(() => {
        expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
      });

      const goBackButton = screen.getByText('Go Back');
      await user.click(goBackButton);

      await waitFor(() => {
        expect(screen.queryByText('Go back?')).toBeInTheDocument();
      });

      const confirmButton = screen.getByText('Yes, Cancel');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(screen.queryByText('Go back?')).not.toBeInTheDocument();
      });
    });

    it('should close modal when clicking Back button', async () => {
      const user = userEvent.setup();
      render(<MockedReconcileReport />);

      await waitFor(() => {
        expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
      });

      const goBackButton = screen.getByText('Go Back');
      await user.click(goBackButton);

      await waitFor(() => {
        expect(screen.queryByText('Go back?')).toBeInTheDocument();
      });

      const backButton = screen.getByText('Back');
      await user.click(backButton);

      await waitFor(() => {
        const modalTitle = screen.queryByText('Go back?');
        expect(modalTitle).not.toBeInTheDocument();
      });
    });
    it('should handle the confirmation flow when going back', async () => {
      const user = userEvent.setup();

      useReconciliationStore.setState({
        startReconciliationData: {
          processor: 'test-processor',
          payment_type: 'payin',
          report_start_date: '2024-01-01',
          report_end_date: '2024-01-31',
          processor_file_id: '123',
          processor_file_details: { key: 'test-key' },
          field_mapping: {
            processor: 'test-processor',
            payment_type: 'payin',
            primary_key_mappings: [],
            comparison_key_mappings: []
          }
        },
        primaryKeyMappings: [],
        comparisonKeyMappings: []
      });

      render(<MockedReconcileReport />);

      await waitFor(() => {
        expect(screen.getByText('Reconcile reports')).toBeInTheDocument();
      });

      const goBackButton = screen.getByText('Go Back');
      await user.click(goBackButton);

      await waitFor(() => {
        expect(screen.queryByText('Go back?')).toBeInTheDocument();
      });

      const confirmButton = screen.getByText('Yes, Cancel');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(screen.queryByText('Go back?')).not.toBeInTheDocument();
      });

      const storeState = useReconciliationStore.getState();
      expect(storeState.startReconciliationData.processor).toBe('test-processor');
    });
  });
});
